"""
Herbit Worker Service - Minimal Version
Only processes question generation tasks from Dapr pubsub.
Uses PostgreSQL ONLY for storing generated questions (business data).
Uses Dapr for ALL operational data (task status, DLQ, etc.).
"""

import asyncio
import json
import os
import time
from datetime import datetime
from typing import Optional

import httpx
from app.services.create_quiz_questions import ask_for_question
from app.utils.logger import debug, error, info, warning
from fastapi import FastAPI, HTTPException, Request
from pydantic import BaseModel

# FastAPI app
app = FastAPI(
    title="Herbit Worker Service",
    description="Processes question generation tasks via Dapr",
    version="1.0.0",
)

# ============================================================================
# MODELS
# ============================================================================


class QuestionGenerationTask(BaseModel):
    task_id: str
    skill_id: int
    skill_name: str
    skill_description: str
    created_at: Optional[str] = None


# ============================================================================
# STARTUP
# ============================================================================


@app.on_event("startup")
async def startup_event():
    """Initialize worker service"""
    info("Worker service starting...")

    # Start Dapr connectivity check in background (non-blocking)
    info("Starting Dapr connectivity check in background...")
    asyncio.create_task(initialize_dapr_connectivity())

    info("Worker service started - ready to serve requests")


# ============================================================================
# CORE FUNCTIONS
# ============================================================================


async def initialize_dapr_connectivity():
    """
    Initialize Dapr connectivity in the background without blocking application startup.
    """
    # Give Dapr sidecar a moment to start (configurable delay)
    startup_delay = float(os.getenv("DAPR_STARTUP_DELAY", "2.0"))
    await asyncio.sleep(startup_delay)

    # Wait for Dapr sidecar to be ready (2 minutes max with exponential backoff)
    if await wait_for_dapr_ready(max_wait_time=120):
        info("Dapr connectivity established - processing tasks via Dapr")
    else:
        warning("Dapr sidecar is not ready - some features may not work")


async def wait_for_dapr_ready(max_wait_time: int = 120) -> bool:
    """
    Wait for Dapr sidecar to be ready using exponential backoff.
    Returns True if Dapr is ready, False if timeout.

    Args:
        max_wait_time: Maximum time to wait in seconds (default: 2 minutes)
    """
    dapr_port = os.getenv("DAPR_HTTP_PORT", "3501")
    health_url = f"http://localhost:{dapr_port}/v1.0/healthz"

    # Exponential backoff: 1s, 2s, 4s, 8s, 15s, 15s, 15s...
    delays = [1, 2, 4, 8, 15]  # Then stick to 15s intervals
    start_time = time.time()
    attempt = 0

    info("Waiting for Dapr sidecar...")

    while time.time() - start_time < max_wait_time:
        try:
            async with httpx.AsyncClient(timeout=3.0) as client:
                response = await client.get(health_url)
                if response.status_code in [
                    200,
                    204,
                ]:  # Dapr health endpoint returns 204
                    elapsed = int(time.time() - start_time)
                    info(f"Dapr sidecar ready after {elapsed}s (attempt {attempt + 1})")
                    return True
        except Exception:
            pass  # Continue trying

        # Calculate delay for this attempt
        delay = delays[min(attempt, len(delays) - 1)]

        # Log progress less frequently
        elapsed = int(time.time() - start_time)
        if attempt == 0 or elapsed % 30 == 0:  # Log every 30 seconds after first attempt
            remaining = max_wait_time - elapsed
            info(f"Still waiting for Dapr sidecar... ({elapsed}s elapsed, {remaining}s remaining)")

        await asyncio.sleep(delay)
        attempt += 1

    elapsed = int(time.time() - start_time)
    error(f"Dapr sidecar not ready after {elapsed}s")
    return False


async def call_dapr_with_retry(
    method: str,
    url: str,
    json_data: Optional[dict] = None,
    max_retries: int = 3,
    base_delay: float = 1.0,
    timeout: float = 10.0,
) -> Optional[httpx.Response]:
    """
    Make HTTP calls to Dapr with retry logic and exponential backoff.
    """
    for attempt in range(max_retries):
        try:
            async with httpx.AsyncClient(timeout=timeout) as client:
                if method.upper() == "GET":
                    response = await client.get(url)
                elif method.upper() == "POST":
                    response = await client.post(url, json=json_data)
                else:
                    raise ValueError(f"Unsupported HTTP method: {method}")

                # Return response for both success and client errors (4xx)
                # Only retry on server errors (5xx) and connection errors
                if response.status_code < 500:
                    return response

                warning(f"Dapr call failed with status {response.status_code}, attempt {attempt + 1}/{max_retries}")

        except (httpx.ConnectError, httpx.TimeoutException, httpx.NetworkError) as e:
            if attempt == max_retries - 1:  # Only log on final attempt
                warning(f"Dapr connection error: {e}")
        except Exception as e:
            if attempt == max_retries - 1:  # Only log on final attempt
                error(f"Unexpected error calling Dapr: {e}")

        if attempt < max_retries - 1:
            delay = base_delay * (2**attempt)  # Exponential backoff
            await asyncio.sleep(delay)

    error(f"All {max_retries} Dapr call attempts failed for {method} {url}")
    return None


async def store_task_status_in_dapr(
    task_id: str,
    status: str,
    skill_id: int,
    skill_name: str,
    questions_generated: int = 0,
    error_message: Optional[str] = None,
):
    """Store task status in Dapr state store (operational data only)"""
    dapr_port = os.getenv("DAPR_HTTP_PORT", "3501")

    task_data = {
        "task_id": task_id,
        "status": status,
        "skill_id": skill_id,
        "skill_name": skill_name,
        "questions_generated": questions_generated,
        "error_message": error_message,
        "timestamp": datetime.utcnow().isoformat(),
    }

    state_data = [{"key": f"task-{task_id}", "value": task_data}]
    save_url = f"http://localhost:{dapr_port}/v1.0/state/statestore"

    response = await call_dapr_with_retry("POST", save_url, json_data=state_data)

    if response and response.status_code in [200, 201, 204]:
        debug(f"Task status stored in Dapr: {task_id} -> {status}")
    else:
        status_code = response.status_code if response else "No response"
        error(f"Failed to store task status in Dapr: HTTP {status_code}")


async def process_question_generation(task: QuestionGenerationTask) -> tuple[bool, int]:
    """
    Process question generation for a skill.
    Returns (success, questions_generated)
    """
    start_time = time.time()

    try:
        info(f"Starting question generation for skill: {task.skill_name} (ID: {task.skill_id})")

        # Generate questions using the existing function
        # This function will store questions in PostgreSQL (business data)
        quiz_name = f"skill_{task.skill_id}_{task.skill_name}"
        topics = task.skill_description

        result = await ask_for_question(quiz_name, topics, skill_id=task.skill_id, skill_name=task.skill_name)

        processing_time = time.time() - start_time

        if result:
            # The ask_for_question function should return the number of questions generated
            # If it doesn't, we'll assume 1 question was generated
            questions_generated = result if isinstance(result, int) else 1

            info(f"Generated {questions_generated} questions for skill: {task.skill_name} in {processing_time:.2f}s")
            return True, questions_generated
        else:
            error(f"Failed to generate questions for skill: {task.skill_name} after {processing_time:.2f}s")
            return False, 0

    except Exception as e:
        processing_time = time.time() - start_time
        error(
            f"Error in question generation for skill {task.skill_name} after {processing_time:.2f}s: {e}",
            exception=e,
        )
        return False, 0


# ============================================================================
# DAPR ENDPOINTS (REQUIRED FOR PUBSUB)
# ============================================================================


@app.get("/dapr/subscribe")
async def dapr_subscribe():
    """
    Dapr subscription configuration endpoint.
    This tells Dapr what topics this service is subscribed to.
    """
    subscriptions = [
        {"pubsubname": "pubsub", "topic": "question-tasks", "route": "/question-tasks"},
        {
            "pubsubname": "pubsub",
            "topic": "question-tasks-dlq",
            "route": "/dlq-handler",
        },
    ]

    info(f"Registering Dapr subscriptions: {subscriptions}")
    return subscriptions


@app.post("/question-tasks")
async def handle_question_task(request: Request):
    """Handle question generation tasks from Dapr pubsub"""
    try:
        body = await request.body()
        info(f"Received task: {body.decode()}")

        data = json.loads(body)
        task_data = data.get("data", data)

        # Parse task
        try:
            task = QuestionGenerationTask(**task_data)
        except Exception as e:
            error(f"Invalid task data format: {e}")
            raise HTTPException(status_code=400, detail=f"Invalid task data: {str(e)}")

        # Check if task already processed (deduplication using Dapr)
        dapr_port = os.getenv("DAPR_HTTP_PORT", "3501")
        task_url = f"http://localhost:{dapr_port}/v1.0/state/statestore/task-{task.task_id}"

        response = await call_dapr_with_retry("GET", task_url)

        if response and response.status_code == 200:
            try:
                existing_task = response.json()
                if existing_task.get("status") == "completed":
                    info(f"Task {task.task_id} already completed. Skipping.")
                    return  # HTTP 200 with empty body for Dapr pub/sub success
                elif existing_task.get("status") == "processing":
                    info(f"Task {task.task_id} is already being processed. Skipping.")
                    return  # HTTP 200 with empty body for Dapr pub/sub success
            except Exception as e:
                error(f"Failed to parse existing task data: {e}")
        elif response is None:
            warning(f"Could not check task duplication for {task.task_id} - proceeding anyway")

        # Mark as processing in Dapr
        await store_task_status_in_dapr(task.task_id, "processing", task.skill_id, task.skill_name)

        # Process the task with timeout
        timeout_seconds = int(os.getenv("QUESTION_GENERATION_TIMEOUT", "600"))  # 10 minutes

        try:
            success, questions_generated = await asyncio.wait_for(
                process_question_generation(task), timeout=timeout_seconds
            )

            if success:
                # Store success status in Dapr
                await store_task_status_in_dapr(
                    task.task_id,
                    "completed",
                    task.skill_id,
                    task.skill_name,
                    questions_generated,
                )

                info(f"Successfully generated {questions_generated} questions for skill: {task.skill_name}")
                return  # HTTP 200 with empty body for Dapr pub/sub success
            else:
                # Store failure status in Dapr
                await store_task_status_in_dapr(
                    task.task_id,
                    "failed",
                    task.skill_id,
                    task.skill_name,
                    0,
                    "Question generation failed",
                )

                # Return 500 to trigger Dapr retries
                raise HTTPException(
                    status_code=500,
                    detail=f"Question generation failed for skill: {task.skill_name}",
                )

        except asyncio.TimeoutError:
            error_msg = f"Timeout after {timeout_seconds} seconds"
            error(f"Task {task.task_id} timed out: {error_msg}")

            # Store timeout status in Dapr
            await store_task_status_in_dapr(task.task_id, "failed", task.skill_id, task.skill_name, 0, error_msg)

            # Return 500 to trigger retries
            raise HTTPException(status_code=500, detail=f"Question generation timed out: {error_msg}")

    except json.JSONDecodeError as e:
        error(f"Failed to parse request body: {e}")
        raise HTTPException(status_code=400, detail="Invalid JSON in request body")
    except Exception as e:
        error(f"Error processing task: {e}", exception=e)
        # Return 500 to trigger Dapr retries
        raise HTTPException(status_code=500, detail=f"Task processing failed: {str(e)}")


@app.post("/dlq-handler")
async def handle_dlq_task(request: Request):
    """Handle tasks from Dead Letter Queue - store in Dapr for manual review"""
    try:
        body = await request.body()
        warning(f"Received DLQ message: {body.decode()}")

        data = json.loads(body)
        task_data = data.get("data", data)
        task_id = task_data.get("task_id", "unknown")

        # Store DLQ task in Dapr state store for manual review
        dapr_port = os.getenv("DAPR_HTTP_PORT", "3501")

        dlq_data = {
            "task_id": task_id,
            "original_data": task_data,
            "failure_reason": "Failed after maximum retries",
            "created_at": datetime.utcnow().isoformat(),
            "requires_manual_review": True,
        }

        state_data = [{"key": f"dlq-{task_id}", "value": dlq_data}]
        save_url = f"http://localhost:{dapr_port}/v1.0/state/statestore"

        response = await call_dapr_with_retry("POST", save_url, json_data=state_data)

        if response and response.status_code in [200, 201, 204]:
            warning(f"DLQ task stored in Dapr for manual review: {task_id}")
        else:
            error(f"Failed to store DLQ task in Dapr: {task_id}")

        info(f"📥 DLQ task {task_id} stored for manual review")
        return  # HTTP 200 with empty body for Dapr pub/sub success

    except Exception as e:
        error(f"Error handling DLQ task: {e}")
        raise HTTPException(status_code=500, detail=f"DLQ handling failed: {str(e)}")


# ============================================================================
# MINIMAL ENDPOINTS (ONLY HEALTH CHECK)
# ============================================================================


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    # Check Dapr connectivity
    dapr_status = "unknown"
    try:
        dapr_port = os.getenv("DAPR_HTTP_PORT", "3501")
        health_url = f"http://localhost:{dapr_port}/v1.0/healthz"

        async with httpx.AsyncClient(timeout=2.0) as client:
            response = await client.get(health_url)
            dapr_status = "healthy" if response.status_code in [200, 204] else f"unhealthy ({response.status_code})"
    except Exception as e:
        dapr_status = f"unreachable ({str(e)[:50]})"

    return {
        "status": "healthy",
        "service": "herbit-worker",
        "dapr_status": dapr_status,
        "timestamp": datetime.utcnow().isoformat(),
    }


if __name__ == "__main__":
    import uvicorn

    port = int(os.getenv("PORT", "8001"))
    reload_mode = os.getenv("WORKER_RELOAD", "false").lower() == "true"

    # Configure uvicorn to use our centralized logging
    uvicorn.run(
        "worker:app",
        host="0.0.0.0",
        port=port,
        reload=reload_mode,
        access_log=False,  # Disable uvicorn's access logging
        log_config=None,  # Use our logging configuration
    )
