/**
 * Modal/Dialog Management Composable
 *
 * Provides reusable modal state management with:
 * - Open/close state management
 * - Escape key handling
 * - Body scroll prevention
 * - Backdrop click handling
 * - Cleanup on unmount
 */
import { ref, onMounted, onUnmounted } from "vue";

export function useModal(options = {}) {
  const {
    closeOnEscape = true,
    preventBodyScroll = true,
    closeOnBackdrop = true,
    onOpen = null,
    onClose = null,
  } = options;

  // Modal state
  const isOpen = ref(false);
  const isAnimating = ref(false);

  // Store original body overflow for restoration
  let originalBodyOverflow = "";
  let originalDocumentOverflow = "";

  /**
   * Open the modal
   */
  const open = () => {
    if (isOpen.value) return;

    isOpen.value = true;
    isAnimating.value = true;

    // Prevent body scrolling if enabled
    if (preventBodyScroll) {
      originalBodyOverflow = document.body.style.overflow;
      originalDocumentOverflow = document.documentElement.style.overflow;
      document.body.style.overflow = "hidden";
      document.documentElement.style.overflow = "hidden";
    }

    // Add escape key listener if enabled
    if (closeOnEscape) {
      document.addEventListener("keydown", handleEscapeKey);
    }

    // Call onOpen callback if provided
    if (onOpen && typeof onOpen === "function") {
      onOpen();
    }

    // Reset animation state after a short delay
    setTimeout(() => {
      isAnimating.value = false;
    }, 300);
  };

  /**
   * Close the modal
   */
  const close = () => {
    if (!isOpen.value) return;

    isAnimating.value = true;

    // Restore body scrolling if it was prevented
    if (preventBodyScroll) {
      document.body.style.overflow = originalBodyOverflow;
      document.documentElement.style.overflow = originalDocumentOverflow;
    }

    // Remove escape key listener
    if (closeOnEscape) {
      document.removeEventListener("keydown", handleEscapeKey);
    }

    // Call onClose callback if provided
    if (onClose && typeof onClose === "function") {
      onClose();
    }

    // Close after animation
    setTimeout(() => {
      isOpen.value = false;
      isAnimating.value = false;
    }, 200);
  };

  /**
   * Toggle modal state
   */
  const toggle = () => {
    if (isOpen.value) {
      close();
    } else {
      open();
    }
  };

  /**
   * Handle escape key press
   */
  const handleEscapeKey = (event) => {
    if (event.key === "Escape" && isOpen.value && closeOnEscape) {
      close();
    }
  };

  /**
   * Handle backdrop click
   */
  const handleBackdropClick = (event) => {
    if (closeOnBackdrop && event.target === event.currentTarget) {
      close();
    }
  };

  /**
   * Cleanup function to remove event listeners
   */
  const cleanup = () => {
    if (closeOnEscape) {
      document.removeEventListener("keydown", handleEscapeKey);
    }

    // Restore body scrolling if modal is still open
    if (isOpen.value && preventBodyScroll) {
      document.body.style.overflow = originalBodyOverflow;
      document.documentElement.style.overflow = originalDocumentOverflow;
    }
  };

  // Cleanup on unmount
  onUnmounted(() => {
    cleanup();
  });

  return {
    // State
    isOpen,
    isAnimating,

    // Methods
    open,
    close,
    toggle,
    handleBackdropClick,
    cleanup,
  };
}

/**
 * Specialized modal composable for detailed results popups
 * Used in quiz components for showing detailed results
 */
export function useDetailedResultsModal() {
  return useModal({
    closeOnEscape: true,
    preventBodyScroll: true,
    closeOnBackdrop: true,
  });
}

/**
 * Specialized modal composable for confirmation dialogs
 * Used for delete confirmations and other critical actions
 */
export function useConfirmationModal() {
  return useModal({
    closeOnEscape: true,
    preventBodyScroll: false,
    closeOnBackdrop: false, // Don't close on backdrop for confirmations
  });
}
