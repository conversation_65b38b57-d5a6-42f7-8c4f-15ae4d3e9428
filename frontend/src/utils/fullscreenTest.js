/**
 * Simple test utility to verify fullscreen functionality
 */

export const testFullscreenSupport = () => {
  const element = document.documentElement;

  const hasFullscreenSupport = !!(
    element.requestFullscreen ||
    element.webkitRequestFullscreen ||
    element.msRequestFullscreen
  );

  console.log("Fullscreen API Support:", {
    standard: !!element.requestFullscreen,
    webkit: !!element.webkitRequestFullscreen,
    ms: !!element.msRequestFullscreen,
    overall: hasFullscreenSupport,
  });

  return hasFullscreenSupport;
};

export const getCurrentFullscreenState = () => {
  const isFullscreen = !!(
    document.fullscreenElement ||
    document.webkitFullscreenElement ||
    document.msFullscreenElement
  );

  console.log("Current fullscreen state:", isFullscreen);
  return isFullscreen;
};
