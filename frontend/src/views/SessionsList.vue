<template>
  <PhantomLayout title="Sessions" :no-scroll="true">
    <div class="p-6 -mt-10">
      <!-- Loading indicator -->
      <div v-if="isLoading" class="flex justify-center items-center py-12">
        <div
          class="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-phantom-blue"
        />
        <span class="ml-3 text-white/80">Loading sessions...</span>
      </div>

      <!-- Error message -->
      <div
        v-if="message && !isSuccess"
        class="bg-red-500/10 backdrop-blur-sm border border-red-500/30 text-white px-6 py-4 rounded-xl mb-6 flex items-center"
      >
        <!-- Error Icon -->
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5 mr-3 text-red-400"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
          />
        </svg>
        {{ message }}
      </div>

      <!-- Success message -->
      <div
        v-if="message && isSuccess"
        class="bg-green-500/10 backdrop-blur-sm border border-green-500/30 text-white px-6 py-4 rounded-xl mb-6 flex items-center"
      >
        <!-- Success Icon -->
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-5 w-5 mr-3 text-green-400"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M5 13l4 4L19 7"
          />
        </svg>
        {{ message }}
      </div>

      <!-- Header: Search bar and Generate Sessions button -->
      <div
        class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6"
      >
        <div class="w-full flex flex-row items-center">
          <div class="relative flex-1 mr-3">
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Search sessions..."
              class="w-full bg-white/5 border border-white/10 rounded-xl px-4 py-2.5 pl-10 text-white focus:outline-none focus:ring-2 focus:ring-phantom-blue/50"
            />
            <div
              class="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50"
            >
              <!-- Search Icon -->
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
            </div>
          </div>
          <button
            class="btn-phantom px-5 py-2.5 text-sm whitespace-nowrap"
            @click="navigateTo('/generate-sessions')"
          >
            <span class="flex items-center">
              <!-- Plus Icon -->
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-4 w-4 mr-1.5"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 4v16m8-8H4"
                />
              </svg>
              Generate Sessions
            </span>
          </button>
        </div>
      </div>

      <!-- SESSIONS TAB CONTENT -->
      <div v-if="activeTab === 'sessions'">
        <!-- No sessions message -->
        <div
          v-if="!isLoading && sessions.pendingSessions.value.length === 0"
          class="text-center py-16"
        >
          <div
            class="w-20 h-20 mx-auto mb-6 rounded-full bg-white/5 flex items-center justify-center"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-10 w-10 text-white/40"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"
              />
            </svg>
          </div>
          <h3 class="text-xl font-medium text-white mb-2">
            No pending sessions found
          </h3>
          <p class="text-white/60 mb-6">Generate new sessions to get started</p>
        </div>

        <!-- Sessions table -->
        <div
          v-if="!isLoading && sessions.pendingSessions.value.length > 0"
          class="rounded-xl border border-white/10 backdrop-blur-sm"
        >
          <table class="w-full text-left">
            <thead>
              <tr class="border-b border-white/10 bg-white/5">
                <th class="py-4 px-6 text-white/80 font-medium">Username</th>
                <th class="py-4 px-6 text-white/80 font-medium">
                  Session Code
                </th>
                <th class="py-4 px-6 text-white/80 font-medium">Assessment</th>
                <th class="py-4 px-6 text-white/80 font-medium">Status</th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="(session, index) in paginatedPendingSessions"
                :key="index"
                :class="index % 2 === 0 ? 'bg-white/[0.03]' : 'bg-transparent'"
                class="border-b border-white/5 hover:bg-white/[0.05] transition-colors duration-150 cursor-pointer"
                @click="viewSessionDetails(session)"
              >
                <td class="py-4 px-6 text-white font-medium">
                  {{ session.username }}
                </td>
                <td class="py-4 px-6">
                  <span
                    class="font-mono bg-white/5 text-phantom-blue px-3 py-1 rounded-full border border-phantom-blue/20"
                  >
                    {{ getDisplaySessionCode(session) }}
                  </span>
                </td>
                <td class="py-4 px-6 text-white/80">
                  {{ session.assessment_name }}
                </td>
                <td class="py-4 px-6">
                  <span
                    class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-yellow-500/10 text-yellow-400 border border-yellow-500/20"
                  >
                    Pending
                  </span>
                </td>
              </tr>
            </tbody>
          </table>

          <!-- Pagination for pending sessions -->
          <div v-if="totalPendingPages > 1" class="border-t border-white/10">
            <Pagination
              :current-page="currentPendingPage"
              :total-pages="totalPendingPages"
              :total-items="totalPendingSessions.value"
              :items-per-page="itemsPerPage"
              @page-change="onPendingPageChange"
            />
          </div>
        </div>

        <!-- Completed Sessions Section -->
        <div v-if="sessions.completedSessions.value.length > 0" class="mt-8">
          <h3 class="text-xl font-medium text-white mb-4">
            Completed Sessions
          </h3>

          <div class="rounded-xl border border-white/10 backdrop-blur-sm">
            <table class="w-full text-left">
              <thead>
                <tr class="border-b border-white/10 bg-white/5">
                  <th class="py-4 px-6 text-white/80 font-medium">Username</th>
                  <th class="py-4 px-6 text-white/80 font-medium">
                    Session Code
                  </th>
                  <th class="py-4 px-6 text-white/80 font-medium">
                    Assessment
                  </th>
                  <th class="py-4 px-6 text-white/80 font-medium">Status</th>
                  <th class="py-4 px-6 text-white/80 font-medium">Score</th>
                </tr>
              </thead>
              <tbody>
                <tr
                  v-for="(session, index) in paginatedCompletedSessions"
                  :key="`completed-${index}`"
                  :class="
                    index % 2 === 0 ? 'bg-white/[0.03]' : 'bg-transparent'
                  "
                  class="border-b border-white/5 hover:bg-white/[0.05] transition-colors duration-150 cursor-pointer"
                  @click="viewSessionDetails(session)"
                >
                  <td class="py-4 px-6 text-white font-medium">
                    {{ session.username }}
                  </td>
                  <td class="py-4 px-6">
                    <span
                      class="font-mono bg-white/5 text-phantom-blue px-3 py-1 rounded-full border border-phantom-blue/20"
                    >
                      {{ getDisplaySessionCode(session) }}
                    </span>
                  </td>
                  <td class="py-4 px-6 text-white/80">
                    {{ session.assessment_name }}
                  </td>
                  <td class="py-4 px-6">
                    <span
                      class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-500/10 text-green-400 border border-green-500/20"
                    >
                      Completed
                    </span>
                  </td>
                  <td class="py-4 px-6">
                    <span class="font-medium text-white">
                      {{ session.score || "N/A" }}
                    </span>
                  </td>
                </tr>
              </tbody>
            </table>

            <!-- Pagination for completed sessions -->
            <div
              v-if="totalCompletedPages > 1"
              class="border-t border-white/10"
            >
              <Pagination
                :current-page="currentCompletedPage"
                :total-pages="totalCompletedPages"
                :total-items="totalCompletedSessions.value"
                :items-per-page="itemsPerPage"
                @page-change="onCompletedPageChange"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- ASSESSMENTS TAB CONTENT -->
      <div v-if="activeTab === 'assessments'">
        <!-- No assessments message -->
        <div
          v-if="!isLoading && assessments.length === 0"
          class="text-center py-16"
        >
          <div
            class="w-20 h-20 mx-auto mb-6 rounded-full bg-white/5 flex items-center justify-center"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-10 w-10 text-white/40"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
              />
            </svg>
          </div>
          <h3 class="text-xl font-medium text-white mb-2">
            No assessments found
          </h3>
          <p class="text-white/60 mb-6">
            Get started by creating your first assessment
          </p>
        </div>

        <!-- Assessments table -->
        <div
          v-if="!isLoading && assessments.length > 0"
          class="rounded-xl border border-white/10 backdrop-blur-sm"
        >
          <table class="w-full text-left">
            <thead>
              <tr class="border-b border-white/10 bg-white/5">
                <th class="py-4 px-6 text-white/80 font-medium">Name</th>
                <th class="py-4 px-6 text-white/80 font-medium">Mode</th>
                <th class="py-4 px-6 text-white/80 font-medium">Questions</th>
                <th class="py-4 px-6 text-white/80 font-medium">Duration</th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="(assessment, index) in filteredAssessments"
                :key="assessment.id"
                :class="index % 2 === 0 ? 'bg-white/[0.03]' : 'bg-transparent'"
                class="border-b border-white/5 hover:bg-white/[0.05] transition-colors duration-150 cursor-pointer"
                @click="navigateToAssessmentDetail(assessment)"
              >
                <td class="py-4 px-6 text-white font-medium">
                  {{ assessment.name }}
                </td>
                <td class="py-4 px-6">
                  <span
                    class="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium"
                    :class="
                      assessment.question_selection_mode === 'fixed'
                        ? 'bg-phantom-indigo/20 text-phantom-indigo border border-phantom-indigo/30'
                        : 'bg-phantom-blue/20 text-phantom-blue border border-phantom-blue/30'
                    "
                  >
                    {{
                      assessment.question_selection_mode === "fixed"
                        ? "Fixed"
                        : "Dynamic"
                    }}
                  </span>
                </td>
                <td class="py-4 px-6 text-white/80">
                  <div class="flex items-center">
                    <span
                      :title="`${assessment.total_questions || 0} questions`"
                      class="cursor-help"
                    >
                      {{ assessment.total_questions || "N/A" }}
                    </span>
                  </div>
                </td>
                <td class="py-4 px-6 text-white/80">
                  {{
                    assessment.duration_minutes
                      ? `${assessment.duration_minutes} min`
                      : "N/A"
                  }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- SKILLS TAB CONTENT -->
      <div v-if="activeTab === 'skills'">
        <!-- No skills message -->
        <div v-if="!isLoading && skills.length === 0" class="text-center py-16">
          <div
            class="w-20 h-20 mx-auto mb-6 rounded-full bg-white/5 flex items-center justify-center"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-10 w-10 text-white/40"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"
              />
            </svg>
          </div>
          <h3 class="text-xl font-medium text-white mb-2">No skills found</h3>
          <p class="text-white/60 mb-6">
            Get started by creating your first skill category
          </p>
        </div>

        <!-- Skills table -->
        <div
          v-if="!isLoading && skills.length > 0"
          class="rounded-xl border border-white/10 backdrop-blur-sm"
        >
          <table class="w-full text-left">
            <thead>
              <tr class="border-b border-white/10 bg-white/5">
                <th class="py-4 px-6 text-white/80 font-medium">Name</th>
                <th class="py-4 px-6 text-white/80 font-medium">Description</th>
                <th class="py-4 px-6 text-white/80 font-medium">Questions</th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="(skill, index) in filteredSkills"
                :key="skill.id"
                :class="index % 2 === 0 ? 'bg-white/[0.03]' : 'bg-transparent'"
                class="border-b border-white/5 hover:bg-white/[0.05] transition-colors duration-150 cursor-pointer"
                @click="navigateToSkillDetail(skill)"
              >
                <td class="py-4 px-6">
                  <div class="flex items-center">
                    <div
                      class="w-10 h-10 rounded-full bg-gradient-to-br from-phantom-blue to-phantom-indigo flex items-center justify-center mr-3"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-5 w-5 text-white"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
                        />
                      </svg>
                    </div>
                    <span class="font-medium text-white">{{ skill.name }}</span>
                  </div>
                </td>
                <td class="py-4 px-6 text-white/70 max-w-md">
                  <div class="relative">
                    <div
                      :class="{
                        'line-clamp-2': !expandedDescriptions[skill.id],
                      }"
                    >
                      {{ skill.description || "No description available" }}
                    </div>
                    <div
                      v-if="isLongDescription(skill.description)"
                      class="mt-1"
                    >
                      <button
                        class="text-phantom-blue text-xs hover:text-phantom-blue/80 transition-colors duration-150 flex items-center"
                        @click.stop="toggleDescription(skill.id)"
                      >
                        <span>{{
                          expandedDescriptions[skill.id]
                            ? "Show less"
                            : "View more"
                        }}</span>
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          class="h-3 w-3 ml-1 transition-transform duration-150"
                          :class="
                            expandedDescriptions[skill.id] ? 'rotate-180' : ''
                          "
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M19 9l-7 7-7-7"
                          />
                        </svg>
                      </button>
                    </div>
                  </div>
                </td>
                <td class="py-4 px-6">
                  <div class="flex items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      class="h-5 w-5 text-phantom-blue mr-2"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                    <span class="text-white/80">{{
                      skill.questionCount || 0
                    }}</span>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </PhantomLayout>
</template>

<script setup>
import { ref, computed, onMounted, watch } from "vue";
import { useRouter } from "vue-router";
import { api } from "@/services/api";
import { logError } from "@/utils/errorHandling";
import { useMessageHandler } from "@/utils/messageHandler";
import {
  extractResponseData,
  extractErrorInfo,
} from "@/utils/apiResponseHandler";
import {
  getSessionHashId,
  getDisplaySessionCode,
  getAssessmentHashId,
  getSkillHashId,
} from "@/utils/hashIds";
import {
  useNavigation,
  useSearch,
  usePagination,
  useSessions,
  useAssessments,
  useSkillsForList,
} from "@/composables";
import PhantomLayout from "@/components/layout/Layout.vue";
import { Pagination } from "@/components/ui/pagination";

// Composables
const { navigateTo } = useNavigation();
const search = useSearch({ minLength: 2, debounceMs: 300 });
const sessions = useSessions();
const { totalPendingSessions, totalCompletedSessions } = sessions;
const assessments = useAssessments();
const skills = useSkillsForList();

// Active tab state
const activeTab = ref("sessions");

// Aliases
const isLoading = computed(
  () =>
    sessions.isLoading.value ||
    assessments.isLoading.value ||
    skills.isLoading.value,
);
const message = computed(
  () =>
    sessions.message.value || assessments.message.value || skills.message.value,
);
const isSuccess = computed(
  () =>
    sessions.isSuccess.value ||
    assessments.isSuccess.value ||
    skills.isSuccess.value,
);
const searchQuery = search.searchQuery;

// Pagination
const itemsPerPage = 3;
const currentPendingPage = ref(1);
const currentCompletedPage = ref(1);
const currentAssessmentPage = ref(1);
const currentSkillPage = ref(1);

// Filtered Data
const filteredPendingSessions = computed(() => {
  if (!searchQuery.value || activeTab.value !== "sessions")
    return sessions.pendingSessions.value;
  const query = searchQuery.value.toLowerCase();
  return sessions.pendingSessions.value.filter(
    (s) =>
      s.username.toLowerCase().includes(query) ||
      s.assessment_name.toLowerCase().includes(query) ||
      (s.session_code && s.session_code.toLowerCase().includes(query)),
  );
});

const filteredCompletedSessions = computed(() => {
  if (!searchQuery.value || activeTab.value !== "sessions")
    return sessions.completedSessions.value;
  const query = searchQuery.value.toLowerCase();
  return sessions.completedSessions.value.filter(
    (s) =>
      s.username.toLowerCase().includes(query) ||
      s.assessment_name.toLowerCase().includes(query) ||
      (s.session_code && s.session_code.toLowerCase().includes(query)),
  );
});

const filteredAssessments = computed(() => {
  if (!searchQuery.value) return assessments.assessments.value;
  const query = searchQuery.value.toLowerCase();
  return assessments.assessments.value.filter(
    (a) =>
      a.name.toLowerCase().includes(query) ||
      (a.question_selection_mode &&
        a.question_selection_mode.toLowerCase().includes(query)),
  );
});

const filteredSkills = computed(() => {
  if (!searchQuery.value) return skills.skills.value;
  const query = searchQuery.value.toLowerCase();
  return skills.skills.value.filter(
    (s) =>
      s.name.toLowerCase().includes(query) ||
      (s.description && s.description.toLowerCase().includes(query)),
  );
});

// Paginated Data
const paginatedPendingSessions = computed(() => filteredPendingSessions.value);
const paginatedCompletedSessions = computed(
  () => filteredCompletedSessions.value,
);

// Total Pages
const totalPendingPages = computed(() =>
  Math.ceil(sessions.totalPendingSessions.value / itemsPerPage),
);
const totalCompletedPages = computed(() =>
  Math.ceil(sessions.totalCompletedSessions.value / itemsPerPage),
);
const totalAssessmentPages = computed(() =>
  Math.ceil(assessments.totalAssessments.value / itemsPerPage),
);
const totalSkillPages = computed(() =>
  Math.ceil(skills.totalSkills.value / itemsPerPage),
);

// Navigation
const viewSessionDetails = (session) => {
  const hashId = getSessionHashId(session);
  navigateTo(`/session/${hashId}`);
};
const navigateToAssessmentDetail = (assessment) => {
  const hashId = getAssessmentHashId(assessment);
  navigateTo(`/assessment/${hashId}`);
};
const navigateToSkillDetail = (skill) => {
  const hashId = getSkillHashId(skill);
  navigateTo(`/skill/${hashId}`);
};

// UI State
const expandedDescriptions = ref({});
const toggleDescription = (skillId) => {
  expandedDescriptions.value[skillId] = !expandedDescriptions.value[skillId];
};
const isLongDescription = (description) =>
  description && description.length > 100;

// Data Fetching
const fetchSessions = () => {
  sessions.fetchPendingSessions(currentPendingPage.value, itemsPerPage);
  sessions.fetchCompletedSessions(currentCompletedPage.value, itemsPerPage);
};

// Pagination Handlers
const onPendingPageChange = (page) => {
  currentPendingPage.value = page;
  sessions.fetchPendingSessions(page, itemsPerPage);
};
const onCompletedPageChange = (page) => {
  currentCompletedPage.value = page;
  sessions.fetchCompletedSessions(page, itemsPerPage);
};

// Watchers
watch(activeTab, (newTab) => {
  searchQuery.value = "";
  if (newTab === "sessions") fetchSessions();
  if (newTab === "assessments") assessments.fetchAssessments(1, itemsPerPage);
  if (newTab === "skills") skills.fetchSkills();
});

watch(searchQuery, () => {
  currentPendingPage.value = 1;
  currentCompletedPage.value = 1;
});

// Initial Load
onMounted(fetchSessions);
</script>
