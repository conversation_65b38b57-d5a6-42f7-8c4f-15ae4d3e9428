<template>
  <PhantomLayout>
    <!-- Loading indicator -->
    <div v-if="isLoading" class="flex justify-center items-center py-12">
      <div
        class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-phantom-blue"
      />
      <span class="ml-4 text-white text-lg">Loading skill details...</span>
    </div>

    <!-- Message display with animation -->
    <transition name="fade">
      <div v-if="message" class="mb-6 px-6">
        <div
          :class="
            isSuccess
              ? 'bg-green-500/10 border-green-500/30'
              : 'bg-red-500/10 border-red-500/30'
          "
          class="px-6 py-4 rounded-xl border backdrop-blur-sm text-white flex items-center"
        >
          <div v-if="isSuccess" class="flex-shrink-0 mr-3">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 text-green-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M5 13l4 4L19 7"
              />
            </svg>
          </div>
          <div v-else class="flex-shrink-0 mr-3">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 text-red-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
              />
            </svg>
          </div>
          <div class="flex-grow">
            {{ message }}
          </div>
          <button
            class="flex-shrink-0 ml-2 p-1 rounded-full hover:bg-white/10 transition-colors"
            @click="clearMessage()"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-4 w-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>
      </div>
    </transition>

    <!-- Skill Details -->
    <div v-if="skill && !isLoading" class="w-full max-w-none mx-auto px-8">
      <!-- Header with Back Button -->
      <div class="flex justify-end items-center mb-8">
        <div class="flex space-x-4">
          <button
            class="btn-phantom-secondary px-5 py-2.5"
            @click="navigateToListSkills"
          >
            <span class="flex items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5 mr-2"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M10 19l-7-7m0 0l7-7m-7 7h18"
                />
              </svg>
              Back to Skills
            </span>
          </button>
          <button
            :disabled="isGenerating"
            class="btn-phantom px-5 py-2.5"
            :class="{ 'opacity-75 cursor-not-allowed': isGenerating }"
            @click="generateQuestions"
          >
            <span class="flex items-center">
              <svg
                v-if="isGenerating"
                class="animate-spin -ml-1 mr-2 h-5 w-5 text-white"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  class="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  stroke-width="4"
                />
                <path
                  class="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                />
              </svg>
              <svg
                v-else
                xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5 mr-2"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
                />
              </svg>
              {{
                isGenerating
                  ? "Generating... (up to 5 min)"
                  : "Generate Questions"
              }}
            </span>
          </button>
        </div>
      </div>

      <!-- Two Column Layout -->
      <div class="grid grid-cols-1 xl:grid-cols-2 gap-12">
        <!-- LEFT SIDE -->
        <div class="space-y-10">
          <!-- Skill Basic Information -->
          <section
            class="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-10"
          >
            <!-- Skill Name as Title -->
            <h1
              class="text-4xl font-extrabold text-white mb-8 bg-gradient-to-r from-phantom-blue to-phantom-indigo bg-clip-text text-transparent"
            >
              {{ skill.name }}
            </h1>

            <!-- Description Section -->
            <div>
              <h3 class="text-white text-lg font-medium mb-6">Description</h3>
              <div
                class="bg-white/5 backdrop-blur-sm rounded-xl p-8 max-h-[400px] overflow-y-auto custom-scrollbar border border-white/10"
              >
                <div
                  class="text-white max-w-none prose prose-invert prose-sm markdown-content"
                  v-html="parsedDescription"
                />
              </div>
            </div>
          </section>
        </div>

        <!-- RIGHT SIDE -->
        <div class="space-y-10">
          <!-- Questions Section -->
          <section
            class="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-10"
          >
            <h2
              class="text-xl font-semibold text-white mb-6 bg-gradient-to-r from-phantom-blue to-phantom-indigo bg-clip-text text-transparent"
            >
              Questions ({{ questions.length || 0 }})
            </h2>

            <!-- No questions message -->
            <div
              v-if="!questions || questions.length === 0"
              class="text-center py-16 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="h-20 w-20 mx-auto text-white/20 mb-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
              <p class="text-white text-xl">
                No questions found for this skill
              </p>
              <p class="text-white/60 mt-3 text-base">
                Click the "Generate Questions" button to create questions for
                this skill.
              </p>
            </div>

            <!-- Questions List -->
            <div
              v-else
              class="max-h-[600px] overflow-y-auto custom-scrollbar pr-4"
            >
              <div class="space-y-8">
                <div
                  v-for="(question, index) in sortedQuestions"
                  :key="question.que_id"
                  class="bg-white/5 backdrop-blur-sm p-6 rounded-xl border border-white/10 hover:bg-white/10 transition-colors"
                  :class="{
                    'border-l-4 border-l-green-400/70':
                      question.level === 'easy',
                    'border-l-4 border-l-yellow-400/70':
                      question.level === 'intermediate',
                    'border-l-4 border-l-orange-400/70':
                      question.level === 'advanced',
                  }"
                >
                  <div class="flex items-start justify-between mb-4">
                    <div class="flex items-center space-x-3">
                      <span
                        class="bg-white/10 text-white text-xs font-medium px-3 py-1 rounded-full"
                        >{{ index + 1 }}</span
                      >
                    </div>
                  </div>

                  <!-- Question Text -->
                  <h3
                    class="text-white text-base font-medium mb-4 leading-relaxed"
                  >
                    {{ question.question }}
                  </h3>

                  <!-- Options -->
                  <div class="space-y-2 mb-4">
                    <div
                      v-for="(option, key) in question.options"
                      :key="key"
                      class="flex items-start space-x-3 p-3 rounded-lg"
                      :class="{
                        'bg-green-500/10 border border-green-500/30':
                          key.toLowerCase() === question.answer.toLowerCase(),
                        'bg-white/5 border border-white/10':
                          key.toLowerCase() !== question.answer.toLowerCase(),
                      }"
                    >
                      <span
                        class="text-white/80 font-medium text-sm min-w-[20px]"
                        >{{ key.toUpperCase() }}.</span
                      >
                      <span class="text-white/90 text-sm leading-relaxed">{{
                        option
                      }}</span>
                      <svg
                        v-if="
                          key.toLowerCase() === question.answer.toLowerCase()
                        "
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-4 w-4 text-green-400 flex-shrink-0 mt-0.5 ml-auto"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M5 13l4 4L19 7"
                        />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>
        </div>
      </div>
    </div>
  </PhantomLayout>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import { useRoute, useRouter } from "vue-router";
import { api } from "@/services/api";
import { logError } from "@/utils/errorHandling";
import { useMessageHandler } from "@/utils/messageHandler";
import { logApiResponse } from "@/utils/logger";
import { useLoadingState } from "@/composables";
import { marked } from "marked";
import {
  extractResponseData,
  extractErrorInfo,
} from "@/utils/apiResponseHandler";
import config from "../config/globalConfig.js";
import PhantomLayout from "@/components/layout/Layout.vue";

//----------------------------------------------------------------
// Dependencies & Navigation
//----------------------------------------------------------------
const route = useRoute();
const router = useRouter();
const { message, isSuccess, setErrorMessage, setSuccessMessage, clearMessage } =
  useMessageHandler();

//----------------------------------------------------------------
// Navigation Functions
//----------------------------------------------------------------
const navigateToListSkills = () => {
  router.push({ name: "ListSkills" });
};

//----------------------------------------------------------------
// Component State
//----------------------------------------------------------------
const skillId = ref(null);
const skill = ref(null);
const questions = ref([]);
const isGenerating = ref(false);

// Composables
const loadingState = useLoadingState();

// Aliases for backward compatibility
const isLoading = loadingState.isLoading;

//----------------------------------------------------------------
// Computed Properties
//----------------------------------------------------------------

/**
 * Parses the skill's description from Markdown into HTML for rendering.
 * Configured to handle GitHub Flavored Markdown (GFM) and ensure proper line breaks.
 */
const parsedDescription = computed(() => {
  if (!skill.value?.description) return "";

  marked.setOptions({
    breaks: true,
    gfm: true,
    mangle: false,
    smartLists: true,
  });

  return marked(skill.value.description);
});

/**
 * Sorts questions by difficulty level based on a predefined order in a global config file.
 * This ensures a consistent presentation (e.g., Easy -> Intermediate -> Advanced).
 */
const sortedQuestions = computed(() => {
  if (!questions.value?.length) return [];

  return [...questions.value].sort((a, b) => {
    return (
      config.defaultDifficultyOrder[a.level] -
      config.defaultDifficultyOrder[b.level]
    );
  });
});

//----------------------------------------------------------------
// API & Data Fetching Logic
//----------------------------------------------------------------

/**
 * Main function to load all data for the page.
 * It first fetches the core skill details and, upon success, triggers
 * `fetchSkillQuestions` to load the associated questions.
 */
const fetchSkillDetails = async () => {
  isLoading.value = true;
  clearMessage();
  skillId.value = route.params.id;

  if (!skillId.value) {
    setErrorMessage("Skill ID is missing from the URL.");
    isLoading.value = false;
    return;
  }

  try {
    const skillResponse = await api.admin.getSkill(skillId.value);
    const data = extractResponseData(skillResponse);

    if (!data || (!data.id && !data.id_hash)) {
      throw new Error(
        `Skill with ID ${skillId.value} not found or has invalid data.`,
      );
    }

    skill.value = data;
    await fetchSkillQuestions();
  } catch (err) {
    logError(err, "fetchSkillDetails");
    skill.value = null; // Ensure no stale data is displayed on error.

    if (err.response?.status === 404) {
      setErrorMessage(
        `Skill with ID ${skillId.value} not found. Please check the URL.`,
      );
    } else {
      const errorInfo = extractErrorInfo(err);
      setErrorMessage(errorInfo.message || "Failed to load skill details.");
    }
  } finally {
    isLoading.value = false;
  }
};

/**
 * Fetches the questions associated with the current skill.
 * - `suppressErrors`: Prevents setting an error message, which is useful when this
 *   function is called as a refresh after another successful action.
 */
const fetchSkillQuestions = async (suppressErrors = false) => {
  try {
    const response = await api.admin.getSkillQuestions(skillId.value);
    const data = extractResponseData(response);
    questions.value = data?.questions || (Array.isArray(data) ? data : []);

    if (!suppressErrors) clearMessage();
  } catch (err) {
    // A 404 response is not an error here; it just means the skill has no questions yet.
    // This is a normal and expected state for newly created skills.
    if (err.response?.status === 404) {
      questions.value = [];
    } else {
      logError(err, "fetchSkillQuestions");
      if (!suppressErrors) {
        const errorInfo = extractErrorInfo(err);
        setErrorMessage(errorInfo.message || "Failed to fetch questions.");
      }
    }
  }
};

/**
 * Handles the AI-powered question generation process. Provides immediate feedback to the
 * user for a long-running task and includes robust error handling.
 */
const generateQuestions = async () => {
  if (!skill.value?.id && !skill.value?.id_hash) {
    setErrorMessage("Skill data is not fully loaded. Please refresh the page.");
    return;
  }

  isGenerating.value = true;
  // Provide immediate feedback that the process has started, as it can take several minutes.
  setSuccessMessage(
    "Question generation started. This may take up to 5 minutes...",
    0, // Message does not auto-clear.
  );

  try {
    const requestData = {
      skill_id: skill.value.id_hash || skill.value.id, // Use hash ID or numeric ID
      skill_name: skill.value.name,
      skill_description: skill.value.description,
    };

    const response = await api.admin.generateSkillQuestions(requestData);

    logApiResponse(
      "POST",
      "/generate-questions",
      response?.status || 200,
      response?.data,
    );

    // Extract data using our utility function
    const data = extractResponseData(response);

    if (data.success === false) {
      throw new Error(
        data.message || "Question generation failed on the server.",
      );
    }

    const countText = data?.count ? ` (${data.count} questions)` : "";
    setSuccessMessage(`Successfully generated questions${countText}.`);

    // Refresh the questions list, but suppress errors to keep the success message visible.
    // This prevents a minor refresh issue from hiding the fact that generation was successful.
    await fetchSkillQuestions(true);
  } catch (err) {
    logError(err, "generateQuestions");
    // Provide a more helpful message for common timeout errors.
    if (err.code === "ECONNABORTED" || err.message?.includes("timeout")) {
      setErrorMessage(
        "The request timed out, but generation may still be running in the background. Please wait a few moments and then refresh the page.",
      );
    } else {
      // Use our utility function to extract error info
      const errorInfo = extractErrorInfo(err);
      setErrorMessage(
        errorInfo.message ||
          "An unexpected error occurred while generating questions",
      );
    }
  } finally {
    isGenerating.value = false;
  }
};

onMounted(() => {
  fetchSkillDetails();
});
</script>
