<template>
  <section
    class="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-8"
  >
    <h2
      class="text-xl font-semibold text-white mb-6 bg-gradient-to-r from-phantom-blue to-phantom-indigo bg-clip-text text-transparent"
    >
      Fixed Questions Selection
    </h2>

    <!-- Loading State -->
    <div v-if="isLoading" class="flex justify-center items-center py-6">
      <div
        class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-phantom-blue"
      />
      <span class="ml-4 text-white/80">Loading questions...</span>
    </div>

    <!-- No Questions Available -->
    <div
      v-else-if="availableQuestions.length === 0"
      class="text-center py-6 text-white/60"
    >
      <p>No questions available for the selected skills.</p>
      <p class="text-sm mt-2">Please add questions to these skills first.</p>
    </div>

    <!-- Questions Interface -->
    <div v-else class="space-y-6">
      <!-- Question Selection Summary -->
      <div class="bg-white/5 border border-white/10 rounded-lg p-4">
        <h3 class="text-lg font-medium text-white mb-3">Selected Questions</h3>
        <div class="grid grid-cols-3 gap-4 text-sm">
          <div class="text-center">
            <div class="text-2xl font-bold text-green-400">
              {{ questionCountsByLevel.easy }}
            </div>
            <div class="text-white/60">Easy</div>
            <div class="text-xs text-white/40">
              Required: {{ questionCounts.easy }}
            </div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-yellow-400">
              {{ questionCountsByLevel.intermediate }}
            </div>
            <div class="text-white/60">Intermediate</div>
            <div class="text-xs text-white/40">
              Required: {{ questionCounts.intermediate }}
            </div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-red-400">
              {{ questionCountsByLevel.advanced }}
            </div>
            <div class="text-white/60">Advanced</div>
            <div class="text-xs text-white/40">
              Required: {{ questionCounts.advanced }}
            </div>
          </div>
        </div>
        <div class="mt-4 text-center">
          <div class="text-lg font-medium" :class="selectionStatusClass">
            {{ selectionStatusText }}
          </div>
        </div>
      </div>

      <!-- Question Controls -->
      <div class="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
        <!-- Search -->
        <div class="flex-1">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Search questions..."
            class="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-phantom-blue/50"
          />
        </div>

        <!-- Difficulty Filter -->
        <div class="w-full sm:w-auto">
          <select
            v-model="difficultyFilter"
            class="w-full sm:w-auto bg-white/5 border border-white/10 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-phantom-blue/50"
          >
            <option value="all">All Difficulties</option>
            <option value="easy">Easy</option>
            <option value="intermediate">Intermediate</option>
            <option value="advanced">Advanced</option>
          </select>
        </div>

        <!-- Random Selection Button -->
        <button
          type="button"
          class="btn-phantom-secondary px-4 py-2 text-sm whitespace-nowrap"
          @click="selectRandomQuestions"
        >
          Random Select
        </button>

        <!-- Clear Selection Button -->
        <button
          type="button"
          class="btn-phantom-secondary px-4 py-2 text-sm whitespace-nowrap"
          :disabled="selectedQuestionCount === 0"
          @click="clearQuestionSelection"
        >
          Clear All
        </button>
      </div>

      <!-- Questions List -->
      <div
        class="bg-white/5 border border-white/10 rounded-lg max-h-96 overflow-y-auto"
      >
        <div
          v-if="filteredQuestions.length === 0"
          class="text-center py-8 text-white/60"
        >
          No questions match your search criteria.
        </div>
        <div v-else class="divide-y divide-white/10">
          <div
            v-for="question in filteredQuestions"
            :key="question.que_id"
            class="p-4 hover:bg-white/5 cursor-pointer transition-colors"
            :class="{
              'bg-phantom-blue/20': isQuestionSelected(question.que_id),
            }"
            @click="toggleQuestionSelection(question.que_id)"
          >
            <div class="flex items-start justify-between">
              <div class="flex-1">
                <div class="flex items-center gap-2 mb-2">
                  <span
                    class="px-2 py-1 text-xs rounded-full"
                    :class="getDifficultyClass(question.level)"
                  >
                    {{ question.level }}
                  </span>
                  <span class="text-xs text-white/60">
                    {{ question.skill_name }}
                  </span>
                </div>
                <div class="text-white text-sm">
                  {{ question.question }}
                </div>
              </div>
              <div class="ml-4 flex-shrink-0">
                <div
                  class="w-5 h-5 rounded border-2 transition-colors"
                  :class="
                    isQuestionSelected(question.que_id)
                      ? 'bg-phantom-blue border-phantom-blue'
                      : 'border-white/30'
                  "
                >
                  <svg
                    v-if="isQuestionSelected(question.que_id)"
                    class="w-3 h-3 text-white m-0.5"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clip-rule="evenodd"
                    />
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
/**
 * Assessment Fixed Questions Component
 * Handles question selection for fixed-mode assessments
 */
import { computed, watch, onMounted } from "vue";
import { useQuestionSelection } from "@/composables";

const props = defineProps({
  selectedSkillIds: {
    type: Array,
    required: true,
  },
  createdAssessment: {
    type: Object,
    default: null,
  },
});

// Use question selection composable
const questionSelection = useQuestionSelection();

// Destructure composable state and methods
const {
  availableQuestions,
  selectedQuestionIds,
  questionCounts,
  searchQuery,
  difficultyFilter,
  filteredQuestions,
  selectedQuestionCount,
  questionCountsByLevel,
  isSelectionValid,
  isLoading,
  fetchQuestionsForSkills,
  isQuestionSelected,
  toggleQuestionSelection,
  clearQuestionSelection,
  selectRandomQuestions,
} = questionSelection;

// Computed properties
const selectionStatusClass = computed(() => {
  if (isSelectionValid.value) {
    return "text-green-400";
  } else if (selectedQuestionCount.value === 0) {
    return "text-white/60";
  } else {
    return "text-yellow-400";
  }
});

const selectionStatusText = computed(() => {
  if (selectedQuestionCount.value === 0) {
    return "No questions selected";
  } else if (isSelectionValid.value) {
    return "Selection complete";
  } else {
    return "Selection incomplete";
  }
});

// Methods
const getDifficultyClass = (level) => {
  switch (level) {
    case "easy":
      return "bg-green-900/50 text-green-400";
    case "intermediate":
      return "bg-yellow-900/50 text-yellow-400";
    case "advanced":
      return "bg-red-900/50 text-red-400";
    default:
      return "bg-gray-900/50 text-gray-400";
  }
};

// Watchers
watch(
  () => props.selectedSkillIds,
  (newSkillIds) => {
    if (newSkillIds.length > 0) {
      fetchQuestionsForSkills(newSkillIds);
    }
  },
  { immediate: true },
);

// Lifecycle
onMounted(() => {
  if (props.selectedSkillIds.length > 0) {
    fetchQuestionsForSkills(props.selectedSkillIds);
  }
});
</script>

<style scoped>
.btn-phantom-secondary {
  @apply bg-white/5 backdrop-blur-sm border border-white/10 text-white font-medium rounded-lg transition-all duration-200 hover:bg-white/10 hover:border-white/20 disabled:opacity-50 disabled:cursor-not-allowed;
}
</style>
