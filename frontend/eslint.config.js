// /frontend/eslint.config.js

import globals from "globals";
import pluginJs from "@eslint/js";
import pluginVue from "eslint-plugin-vue";
import vueParser from "vue-eslint-parser";
import babelParser from "@babel/eslint-parser";
import prettierConfig from "eslint-config-prettier";

export default [
  // 1. Global configurations and ignores
  {
    ignores: [
      "dist/",
      "frontend/node_modules/",
      "frontend/src/components/ui/**/*", // Your custom ignore for UI components
    ],
  },

  // 2. Base JavaScript rules (from eslint:recommended)
  pluginJs.configs.recommended,

  // 3. Base Vue 3 rules (from eslint-plugin-vue)
  ...pluginVue.configs["flat/recommended"],

  // 4. Your specific configurations and rule overrides for all JS/Vue files
  {
    files: ["**/*.{js,vue}"],
    languageOptions: {
      ecmaVersion: "latest",
      sourceType: "module",
      // Define the necessary parsers for .vue files and <script> blocks
      parser: vueParser,
      parserOptions: {
        parser: babelParser,
        requireConfigFile: false, // Prevents errors if no babel.config.js is found
      },
      // Define standard browser and Node.js global variables
      globals: {
        ...globals.browser,
        ...globals.node,
      },
    },
    rules: {
      // --- Your Custom Rules ---

      // Set line length to 120 to match backend tools
      "max-len": [
        "warn",
        {
          code: 120,
          ignoreUrls: true,
          ignoreStrings: true,
          ignoreTemplateLiterals: true,
        },
      ],
      "vue/max-len": [
        "warn",
        {
          code: 120,
          ignoreUrls: true,
          ignoreStrings: true,
          ignoreTemplateLiterals: true,
        },
      ],

      // Allow specific single-word component names you use frequently
      "vue/multi-word-component-names": [
        "error",
        {
          ignores: [
            "Header",
            "Footer",
            "Hero",
            "Layout",
            "Login",
            "Sessions",
            "Link",
            "Home",
            "Callback",
            "Alert",
            "Button",
            "Calendar",
            "Card",
            "Dialog",
            "Input",
            "Label",
            "Pagination",
            "Select",
            "Tabs",
            "Toggle",
          ],
        },
      ],

      // Keep this as a warning to stay aware of potential XSS risks
      "vue/no-v-html": "warn",

      // Optional: A few other useful rules you might like
      "no-unused-vars": "warn", // Warn about unused variables instead of erroring
      "no-console": "warn", // Remind you to remove console.log statements
    },
  },
  prettierConfig,
];
