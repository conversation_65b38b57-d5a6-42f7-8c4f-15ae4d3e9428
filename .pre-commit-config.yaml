# /.pre-commit-config.yaml

repos:
  # == General Hooks ==
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0 # Updated by autoupdate
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-json

  # == Python Backend Hooks ==
  - repo: https://github.com/psf/black
    rev: 25.1.0 # Updated by autoupdate
    hooks:
      - id: black
        args: ["--line-length=120"]

  - repo: https://github.com/pycqa/flake8
    rev: 7.3.0 # Updated by autoupdate
    hooks:
      - id: flake8
        args: ["--max-line-length=120", "--ignore=E501,W503"]

  - repo: https://github.com/PyCQA/isort
    rev: 6.0.1 # Updated by autoupdate
    hooks:
      - id: isort
        args: ["--profile", "black", "--line-length=120"]

  # == Frontend (Vue/JS) Hook ==
  - repo: https://github.com/pre-commit/mirrors-eslint
    rev: v9.30.1 # Updated by autoupdate
    hooks:
      - id: eslint
        files: ^frontend/.*\.(js|vue)$
        types: [file]
        # === THE FIX IS HERE ===
        # Explicitly tell ESLint where to find the config file,
        # relative to the repository root.
        args: [--fix, "--config", "frontend/eslint.config.js", "--no-warn-ignored"]
        additional_dependencies:
          - eslint@^9.7.0 # These versions don't need to match the rev exactly,
          - "@eslint/js@^9.7.0" # they just need to be compatible. Sticking with the
          - eslint-plugin-vue@^9.27.0 # ones we know work is fine.
          - vue-eslint-parser@^9.4.3
          - "@babel/eslint-parser@^7.24.7"
          - globals@^15.8.0

  # === ADD A NEW HOOK FOR PRETTIER ===
  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v4.0.0-alpha.8 # A recent version of Prettier
    hooks:
      - id: prettier
        # Run on frontend files
        files: ^frontend/.*\.(js|vue|css|html|json)$
